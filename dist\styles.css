/* Reset and base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Open Sans', sans-serif;
    background: linear-gradient(135deg, #f5f1eb 0%, #e8ddd4 100%);
    min-height: 100vh;
    overflow: hidden;
}

/* Container for centering content */
.container {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.content {
    text-align: center;
    max-width: 600px;
    width: 100%;
}

/* Logo styles */
.logo-container {
    margin-bottom: 40px;
}

.logo {
    max-width: 300px;
    max-height: 200px;
    width: auto;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.logo:hover {
    transform: scale(1.05);
}

/* Text logo fallback */
.text-logo h1 {
    font-family: 'Playfair Display', serif;
    font-size: 3.5rem;
    font-weight: 700;
    color: #8b4513;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    margin-bottom: 10px;
}

/* Coming soon text */
.coming-soon h2 {
    font-family: 'Playfair Display', serif;
    font-size: 2.5rem;
    font-weight: 400;
    color: #5d4037;
    margin-bottom: 20px;
    letter-spacing: 2px;
    animation: fadeInUp 1s ease-out;
}

.coming-soon p {
    font-size: 1.2rem;
    color: #6d4c41;
    font-weight: 300;
    letter-spacing: 1px;
    animation: fadeInUp 1s ease-out 0.3s both;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Responsive design */
@media (max-width: 768px) {
    .content {
        padding: 20px;
    }
    
    .logo {
        max-width: 250px;
        max-height: 150px;
    }
    
    .text-logo h1 {
        font-size: 2.5rem;
    }
    
    .coming-soon h2 {
        font-size: 2rem;
        letter-spacing: 1px;
    }
    
    .coming-soon p {
        font-size: 1rem;
    }
}

@media (max-width: 480px) {
    .logo {
        max-width: 200px;
        max-height: 120px;
    }
    
    .text-logo h1 {
        font-size: 2rem;
    }
    
    .coming-soon h2 {
        font-size: 1.5rem;
    }
    
    .coming-soon p {
        font-size: 0.9rem;
    }
}

/* Add a subtle background pattern */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}
