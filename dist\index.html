<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Knead & Nourish - Coming Soon</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;700&family=Open+Sans:wght@300;400&display=swap" rel="stylesheet">
</head>
<body>
    <div class="container">
        <div class="content">
            <div class="logo-container">
                <!-- Replace this with your actual logo -->
                <img src="logo.png" alt="Knead & Nourish Logo" class="logo" id="logo">
                <!-- Fallback text logo if image doesn't load -->
                <div class="text-logo" id="textLogo" style="display: none;">
                    <h1>Knead & Nourish</h1>
                </div>
            </div>
            <div class="coming-soon">
                <h2>Coming Soon!</h2>
                <p>We're baking up something special for you!</p>
            </div>
        </div>
    </div>

    <script>
        // Show text logo if image fails to load
        document.getElementById('logo').onerror = function() {
            this.style.display = 'none';
            document.getElementById('textLogo').style.display = 'block';
        };
    </script>
</body>
</html>
